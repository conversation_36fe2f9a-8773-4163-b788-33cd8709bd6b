import os
import json
from typing import Any
from qdrant_client.models import Filter, FieldCondition, MatchText
from BASE.embeddings.embeddings import generate_embeddings_cloud
from BASE.vdb.qdrant import get_qdrant_client

# -----------------------------------------------------------------------------
# Perform folder search
# -----------------------------------------------------------------------------


async def _perform_folder_search(
    query: str,
    index_name: str,
    folder_path: str,
    limit: int = 10,
    is_local: bool = False,
) -> list[dict[str, Any]]:
    """
    Perform a search within a specific folder using Qdrant.
    Args:
        query: Search query string
        index_name: Name of the collection to search in
        folder_path: Path to folder to search within
        limit: Maximum number of results to return
        is_local: Whether to use local embeddings (default: False)
    Returns:
        List of search results with chunks and metadata
    """
    try:
        # Get Qdrant client
        qc = get_qdrant_client()
        
        # Generate embeddings
        embeddings = await generate_embeddings_cloud(is_local, query)

        # Create folder filter
        # This is a Qdrant filter that matches documents where metadata.file field equals the folder_path
        # Used to restrict search results to files within a specific folder
        folder_filter = Filter(
            must=[
                FieldCondition(
                    key="metadata.file", 
                    match=MatchText(text=folder_path),
                )
            ]
        )

        # Perform search
        search_results = await qc.search(
            collection_name=index_name,
            query_vector=embeddings,
            limit=limit,
            query_filter=folder_filter,
        )

        if not search_results:
            return []

        # Format results
        results = []
        for result in search_results:
            payload = result.payload.get("metadata", {})
            results.append(
                {
                    "file": payload.get("file", ""),
                    "content": {"text": payload.get("content", "")},
                    "additional_metadata": payload.get("additional_metadata", {}),
                }
            )
        return results

    except Exception as e:
        print(f"Error in _perform_folder_search: {e}")
        return []


# -----------------------------------------------------------------------------
# Process folder search
# -----------------------------------------------------------------------------


async def process_folder_search(
    query: str,
    tool_id: str,
    folder_path: str,
    index_name: str,
    search_references,
):
    """Handle folder search actions"""
    try:
        search_results = await _perform_folder_search(
            query=query,
            index_name=index_name,
            folder_path=folder_path,
        )

        for chunk in search_results:
            text = chunk["content"]["text"]
            filename = os.path.basename(chunk["file"])
            filepath = chunk["file"]
            search_references.add_search_result(
                path=filepath, name=filename, content=text, type="file"
            )

        new_messages = [
            {"role": "tool", "name": tool_id, "content": json.dumps(search_results)}
        ]

        return new_messages, search_references
    except Exception as e:
        print(f"Error in process_folder_search: {e}")
        raise


# -----------------------------------------------------------------------------
