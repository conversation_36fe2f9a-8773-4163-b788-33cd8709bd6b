import json
from logger.log import logger
from .swagger.swagger import swagger_list


def process_swagger_context(ctx: dict) -> tuple[str, str]:
    """Process Swagger context and return replacement and additional content
    
    Args:
        ctx (dict): Context dictionary containing Swagger information
        
    Returns:
        tuple[str, str]: (replacement_content, additional_content)
    """
    replacement_content = "Swagger API Documentation"
    additional_content = ""
    
    try:
        # Extract Swagger data from context
        swagger_data = ctx.get("content", {})
        swagger_type = swagger_data.get("type", "content")
        swagger_value = swagger_data.get("value", "")
        full_specs = swagger_data.get("full_specs", False)
        
        # Prepare data for swagger_list function
        data = {
            "type": swagger_type,
            "value": swagger_value,
            "full_specs": full_specs
        }
        
        # Process Swagger content using existing swagger functionality
        import asyncio
        
        # Handle async call in sync context
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an async context, we need to handle this differently
                # For now, we'll create a simple fallback
                result = _process_swagger_sync(data)
            else:
                result = loop.run_until_complete(swagger_list(data))
        except RuntimeError:
            # No event loop running, create one
            result = asyncio.run(swagger_list(data))
        
        # Format the result for chat context
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error processing Swagger context: {result['error']}")
            additional_content = f"\n=====\nSwagger API Error: {result['error']}\n=====\n"
        elif isinstance(result, list):
            # Format endpoints list
            if full_specs:
                additional_content = format_swagger_full_specs(result)
            else:
                additional_content = format_swagger_endpoints(result)
        else:
            additional_content = f"\n=====\nSwagger Content: {json.dumps(result, indent=2)}\n=====\n"
            
    except Exception as e:
        logger.error(f"Error processing Swagger context: {e}")
        additional_content = f"\n=====\nSwagger Processing Error: {str(e)}\n=====\n"
    
    return replacement_content, additional_content


def _process_swagger_sync(data: dict) -> dict:
    """Synchronous fallback for Swagger processing when async context is not available
    
    Args:
        data (dict): Swagger data to process
        
    Returns:
        dict: Processed result or error
    """
    try:
        # Import here to avoid circular imports
        from .swagger.swagger_parser import _extract_endpoints
        
        request_type = data.get("type")
        request_value = data.get("value")
        full_specs = data.get("full_specs", False)
        
        content = None
        
        if request_type == "content":
            try:
                content = json.loads(request_value)
            except json.JSONDecodeError:
                import yaml
                try:
                    content = yaml.safe_load(request_value)
                except Exception as e:
                    return {"error": f"Invalid content format: {e}"}
        else:
            return {"error": "Only 'content' type supported in sync mode"}
        
        if not full_specs:
            return _extract_endpoints(swagger_data=content)
        else:
            # For full specs, we need the async version
            return {"error": "Full specs require async processing"}
            
    except Exception as e:
        return {"error": str(e)}


def format_swagger_endpoints(endpoints: list) -> str:
    """Format simple endpoints list for chat context
    
    Args:
        endpoints (list): List of endpoint dictionaries
        
    Returns:
        str: Formatted endpoints string
    """
    if not endpoints:
        return "\n=====\nSwagger API: No endpoints found\n=====\n"
    
    formatted = "\n=====\nSwagger API Endpoints:\n"
    
    for endpoint in endpoints:
        method = endpoint.get("method", "").upper()
        path = endpoint.get("path", "")
        summary = endpoint.get("summary", "")
        operation_id = endpoint.get("operation_id", "")
        
        formatted += f"\n{method} {path}"
        if summary:
            formatted += f" - {summary}"
        if operation_id:
            formatted += f" (ID: {operation_id})"
        formatted += "\n"
    
    formatted += "=====\n"
    return formatted


def format_swagger_full_specs(specs: list) -> str:
    """Format full endpoint specifications for chat context
    
    Args:
        specs (list): List of full endpoint specifications
        
    Returns:
        str: Formatted specifications string
    """
    if not specs:
        return "\n=====\nSwagger API: No specifications found\n=====\n"
    
    formatted = "\n=====\nSwagger API Full Specifications:\n"
    
    for spec in specs:
        method = spec.get("method", "").upper()
        path = spec.get("path", "")
        spec_data = spec.get("spec", {})
        
        formatted += f"\n--- {method} {path} ---\n"
        formatted += json.dumps(spec_data, indent=2)
        formatted += "\n"
    
    formatted += "=====\n"
    return formatted


def estimate_swagger_token_count(content: str) -> int:
    """Estimate token count for Swagger content
    
    Args:
        content (str): Content to estimate
        
    Returns:
        int: Estimated token count
    """
    return len(content.split()) * 1.3  # Rough approximation


def truncate_swagger_content(content: str, max_tokens: int = 15000) -> str:
    """Truncate Swagger content if it exceeds token limit
    
    Args:
        content (str): Content to potentially truncate
        max_tokens (int): Maximum token limit
        
    Returns:
        str: Potentially truncated content
    """
    estimated_tokens = estimate_swagger_token_count(content)
    
    if estimated_tokens <= max_tokens:
        return content
    
    # Calculate approximate character limit
    char_limit = int((max_tokens / estimated_tokens) * len(content) * 0.9)  # 90% safety margin
    
    if char_limit < len(content):
        truncated = content[:char_limit]
        truncated += "\n\n[Content truncated due to size limit]"
        logger.warning(f"Swagger content truncated from {len(content)} to {len(truncated)} characters")
        return truncated
    
    return content
