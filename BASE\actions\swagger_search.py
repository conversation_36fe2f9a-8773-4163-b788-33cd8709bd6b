import json
from typing import Any
from BASE.embeddings.embeddings import generate_embeddings_cloud
from BASE.vdb.qdrant import get_qdrant_client
import requests
import constants

def query_api(query, results):
    # ["<endpoint> <path></path> <summary></summary> </endpoint>"]
    results_xml: list[str] = []
    for result in results:
        metadata = result.payload["metadata"]
        endpoint_str = "<endpoint>\n"
        endpoint_str += f"  <path>{metadata['name']}</path>\n"
        endpoint_str += f"  <summary>{metadata['content']}</summary>\n"
        endpoint_str += "</endpoint>"
        results_xml.append(endpoint_str)
    with open("coupled_results_str.xml", "w") as f:
        f.write("\n".join(results_xml))

        message_content = f"""
Following are the search results from a swagger API spec. Your task is to select the most relevant endpoints paths based on the provided query.

<query>{query}</query>
<data>{results_xml}</data>

Expected output format:
<paths>
path1
path2
path3
</paths>

If the search results are not relevant to the query, return the paths as:
<paths>
</paths>

STRICT INSTRUCTION:
When returning the paths, make sure to return the exact paths as provided. Do not add any other paths.
YOU MUST PROVIDE AT LEAST 2 PATHS IRRESPECTIVE OF THE QUERY.
"""

    base_url = constants.general

    response = requests.post(
        f"{base_url}/swagger/search",
        json={"content": message_content},
    )
    if response.status_code != 200:
        raise ValueError()

    response_text = response.text.replace("\\n", "\n")
    paths_begin = response_text.find("<paths>")
    paths_end = response_text.find("</paths>")
    paths = response_text[paths_begin + 7 : paths_end].split("\n")
    paths = [path.strip() for path in paths if path.strip()]

    return paths


async def _perform_swagger_search(
    query: str,
    index_name: str,
    limit: int = 20,
) -> list[dict[str, Any]]:
    qc = get_qdrant_client()
    embeddings_backend = generate_embeddings_cloud(False, query)

    try:
        query_embeddings = await embeddings_backend.generate(query)
        results = qc.search(
            collection_name=index_name,
            query_vector=("vectors", query_embeddings),
            limit=limit,
            with_payload=True,
        )

        # name -> swagger endpoint spec
        results_map: dict[str, dict[str, Any]] = {}
        for result in results:
            metadata = result.payload["metadata"]
            results_map[metadata["name"]] = metadata["additional_metadata"]

        api_results = query_api(query=query, results=results)
        filtered_results = list(map(lambda x: results_map[x], api_results))
        return filtered_results
    except Exception as e:
        raise


async def process_swagger_search(
    query: str, tool_id: str, kbid: str, search_references
):
    """Process swagger search actions."""
    try:
        search_results = await _perform_swagger_search(query=query, index_name=kbid)

        for result in search_results:
            search_references.add_search_result(
                type="file",
                name=result.get("name", ""),
                path=result.get("name", ""),
                content=json.dumps(result.get("additional_metadata", {})),
            )

        final_content = json.dumps(search_results)

        return [
            {"role": "tool", "name": tool_id, "content": final_content}
        ], search_references

    except Exception as e:
        print(f"Error in process_swagger_search: {e}")
        raise
